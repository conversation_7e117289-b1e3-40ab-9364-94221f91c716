#include "feilian-specialmodule/oversetMesh/WallDistanceCalculator.h"
#include "feilian-specialmodule/oversetMesh/DynamicWallDistanceManagers.cpp"

namespace Overset
{

    WallDistanceCalculator::WallDistanceCalculator(Mesh *mesh_,
                                                 ZoneManager *zoneManager_,
                                                 const Configure::Flow::FlowConfigure &flowConfig_,
                                                 const MPICommunicator &mpi_world_)
        : localMesh(mesh_), zoneManager(zoneManager_), flowConfig(flowConfig_), mpi_world(mpi_world_),
          processorID(GetMPIRank()), nProcessor(GetMPISize()), wallDistanceManager(nullptr),
          isDynamicMeshEnabled(false), rebuildThreshold(0.1), cacheValidityTime(1.0),
          currentTimeStep(0.0), currentStepNumber(0)
    {
        n_Zones = zoneManager->GetZoneNum();
        dim = localMesh->GetMeshDimension();

        // 初始化动态网格相关数据结构
        dynamicMetrics = DynamicWallDistanceMetrics{};
    }

    WallDistanceCalculator::~WallDistanceCalculator()
    {
        Clear();
    }

    void WallDistanceCalculator::Initialize(Turbulence::WallDistance wallDistMethod_,
                                             AssembleMethod elemTypeMethod_)
    {
        wallDistMethod = wallDistMethod_;
        elemTypeMethod = elemTypeMethod_;
        correctionMethod = WallDistCorrectionMethod::IMPROVED_NORMAL; // 默认使用改进法向量法

        // 清理之前的数据
        Clear();

        // 创建壁面距离管理器
        wallDistanceManager = new WallDistanceManager(wallDistMethod, false); // 不使用投影方式

        // 收集全局壁面数据
        CollectGlobalWallFaces();
    }

    void WallDistanceCalculator::CalculateWallDistances(DonorSearcher *donorSearcher)
    {
        if (wallDistanceManager == nullptr)
        {
            FatalError("OversetWallDistanceCalculator: 壁面距离管理器未初始化");
        }

        wallDistances.resize(n_Zones);
        nearestWallFaceID.resize(n_Zones);

        // 为每个子域计算壁面距离
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            if (globalWallFaceVectors[zoneID].empty())
            {
                // 子域没有壁面，设置为极大值
                wallDistances[zoneID].resize(localMesh->GetElementNumberReal(), INF);
                nearestWallFaceID[zoneID].resize(localMesh->GetElementNumberReal(), -1);
            }
            else
            {
                // 使用WallDistanceManager计算壁面距离
                wallDistanceManager->Calculate(localMesh, globalWallFaceVectors[zoneID],
                                               wallDistances[zoneID], nearestWallFaceID[zoneID]);
            }
        }

        // 修正壁面距离
        if (correctionMethod == WallDistCorrectionMethod::DONOR_SEARCH_BASED && donorSearcher != nullptr)
        {
            CorrectWallDist(donorSearcher);
        }
        else
        {
            CorrectWallDistImproved();
        }
    }

    void WallDistanceCalculator::SetCorrectionMethod(WallDistCorrectionMethod method)
    {
        correctionMethod = method;
    }

    void WallDistanceCalculator::UpdateWallDistField()
    {
        switch (elemTypeMethod)
        {
        case AssembleMethod::ElemBased:
        {
            Scalar minDist;
            List<Scalar> &v_wallDist = localMesh->GetNearWallDistance();
            for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
            {
                minDist = INF;
                for (int zoneID = 0; zoneID < n_Zones; zoneID++)
                {
                    minDist = Min(minDist, wallDistances[zoneID][elemID]);
                }
                v_wallDist[elemID] = minDist;
            }
        }
        break;

        case AssembleMethod::NodeBased:
            // NodeBased方法需要基于节点的壁面距离计算
            // 这里暂时使用简化实现，实际应该基于节点而不是单元中心
            List<Scalar> &v_wallDist = localMesh->GetNearWallDistance();
            for (int elemID = 0; elemID < localMesh->GetNodeNumber(); elemID++)
            {
                Scalar minDist = INF;
                for (int zoneID = 0; zoneID < n_Zones; zoneID++)
                {
                    if (elemID < wallDistances[zoneID].size())
                    {
                        minDist = Min(minDist, wallDistances[zoneID][elemID]);
                    }
                }
                v_wallDist[elemID] = minDist;
            }
            break;

        default:
            break;
        }
    }

    Scalar WallDistanceCalculator::GetWallDistance(int elemID, int zoneID) const
    {
        if (zoneID >= 0 && zoneID < wallDistances.size() &&
            elemID >= 0 && elemID < wallDistances[zoneID].size())
        {
            return wallDistances[zoneID][elemID];
        }
        return INF;
    }

    bool WallDistanceCalculator::IsNearestWallDistToSelf(int elemID, int elemZoneID) const
    {
        for (int otherZoneID = 0; otherZoneID < n_Zones; otherZoneID++)
        {
            if (otherZoneID != elemZoneID &&
                wallDistances[elemZoneID][elemID] > wallDistances[otherZoneID][elemID])
            {
                return false;
            }
        }
        return true;
    }

    void WallDistanceCalculator::Clear()
    {
        // 清理壁面距离管理器
        if (wallDistanceManager != nullptr)
        {
            delete wallDistanceManager;
            wallDistanceManager = nullptr;
        }

        // 清理动态网格管理器
        dynamicManager.reset();
        incrementalUpdater.reset();
        resultCache.reset();
        inheritanceManager.reset();
        loadBalancer.reset();

        // 清理数据容器
        globalWallFaceVectors.clear();
        wallDistances.clear();
        nearestWallFaceID.clear();

        // 清理动态网格相关数据
        previousPositions.clear();
        currentDisplacements.clear();
        elementDisplacements.clear();

        // 重置状态
        isDynamicMeshEnabled = false;
        dynamicMetrics = DynamicWallDistanceMetrics{};
    }

    void WallDistanceCalculator::CollectGlobalWallFaces()
    {
        if (wallDistanceManager == nullptr)
        {
            FatalError("WallDistanceCalculator: 壁面距离管理器未初始化");
        }

        // 收集全局壁面边界面元列表
        globalWallFaceVectors.resize(n_Zones);

        // 为每个子域收集壁面面元
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            // 使用WallDistanceManager的CollectFace方法收集指定子域的壁面
            wallDistanceManager->CollectFace(localMesh, flowConfig, Boundary::Type::WALL,
                                             globalWallFaceVectors[zoneID], zoneID);
        }
    }

    void WallDistanceCalculator::CorrectWallDist(DonorSearcher *donorSearcher)
    {
        if (donorSearcher == nullptr)
        {
            return; // 没有贡献单元搜索器，跳过修正
        }

        List<List<int>> needDonorSearchElem;
        needDonorSearchElem.resize(n_Zones);

        // 第一步：识别需要修正的单元
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const int elemZoneID = zoneManager->GetElemZoneID(elemID);
            for (int zoneI = 0; zoneI < n_Zones; zoneI++)
            {
                const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
                Scalar &wallDist = wallDistances[zoneI][elemID];

                if (zoneI < nearestWallFaceID.size() && elemID < nearestWallFaceID[zoneI].size())
                {
                    int &wallFaceID = nearestWallFaceID[zoneI][elemID];

                    if (wallFaceID >= 0 && wallFaceID < globalWallFaceVectors[zoneI].size())
                    {
                        const Face &wallFace = globalWallFaceVectors[zoneI][wallFaceID].first;
                        const Vector &wallFaceNormal = wallFace.GetNormal();
                        const Vector &wallFaceCenter = wallFace.GetCenter();
                        const auto &nodeList = globalWallFaceVectors[zoneI][wallFaceID].second;

                        Vector elemToFace = wallFaceCenter - elemCenter;
                        Vector unitWallFaceNormal = wallFaceNormal / wallFaceNormal.Mag();
                        Scalar normalDist = elemToFace & unitWallFaceNormal;

                        // 进行几何修正（简化版本）
                        if (normalDist < 0) // 需要进一步判断
                        {
                            if (elemZoneID == zoneI) // 是到自身子域的壁面，修正为正
                            {
                                wallDist = abs(wallDist);
                            }
                            else // 是到其他子域的壁面，需要通过贡献单元搜索判断
                            {
                                needDonorSearchElem[zoneI].push_back(elemID);
                            }
                        }
                    }
                }
            }
        }

        // 第二步：通过贡献单元搜索进行修正
        // 这部分需要与贡献单元搜索器协作，暂时简化实现
        // 实际实现中需要调用donorSearcher的相关方法
    }

    void WallDistanceCalculator::CorrectWallDistImproved()
    {
        // 改进的壁面距离修正方法
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
        {
            const int elemZoneID = zoneManager->GetElemZoneID(elemID);
            const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();

            for (int zoneI = 0; zoneI < n_Zones; zoneI++)
            {
                if (zoneI >= wallDistances.size() || elemID >= wallDistances[zoneI].size())
                    continue;

                Scalar &wallDist = wallDistances[zoneI][elemID];

                if (zoneI < nearestWallFaceID.size() && elemID < nearestWallFaceID[zoneI].size())
                {
                    int wallFaceID = nearestWallFaceID[zoneI][elemID];

                    if (wallFaceID >= 0 && wallFaceID < globalWallFaceVectors[zoneI].size())
                    {
                        const Face &wallFace = globalWallFaceVectors[zoneI][wallFaceID].first;
                        const auto &nodeList = globalWallFaceVectors[zoneI][wallFaceID].second;

                        // 使用改进的几何判断方法
                        bool isInside = IsPointInsideWallImproved(elemCenter, wallFace, nodeList, zoneI, elemZoneID);

                        if (isInside)
                        {
                            wallDist = -abs(wallDist); // 设为负值表示在壁面内部
                        }
                        else
                        {
                            wallDist = abs(wallDist); // 设为正值表示在壁面外部
                        }
                    }
                }
            }
        }
    }

    bool WallDistanceCalculator::IsPointInsideWallImproved(const Vector &point,
                                                       const Face &nearestFace,
                                                       const std::vector<Node> &faceNodes,
                                                       int zoneID,
                                                       int elemZoneID)
    {
        switch (correctionMethod)
        {
        case WallDistCorrectionMethod::RAY_CASTING:
            return IsPointInsideWallRayCasting(point, zoneID);

        case WallDistCorrectionMethod::IMPROVED_NORMAL:
            return IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, elemZoneID == zoneID);

        case WallDistCorrectionMethod::TOPOLOGY_BASED:
            return IsPointInsideWallTopology(point, zoneID);

        case WallDistCorrectionMethod::HYBRID_METHOD:
            return IsPointInsideWallHybrid(point, nearestFace, faceNodes, zoneID, elemZoneID);

        default:
            return IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, elemZoneID == zoneID);
        }
    }

    // ==================== 动态网格支持实现 ====================

    void WallDistanceCalculator::EnableDynamicMesh(Scalar rebuildThreshold, double cacheValidityTime)
    {
        isDynamicMeshEnabled = true;
        this->rebuildThreshold = rebuildThreshold;
        this->cacheValidityTime = cacheValidityTime;

        // 初始化动态网格管理器
        if (!dynamicManager)
        {
            dynamicManager = std::make_unique<DynamicWallDistanceManager>(rebuildThreshold, cacheValidityTime);
        }

        if (!incrementalUpdater)
        {
            incrementalUpdater = std::make_unique<IncrementalWallDistanceUpdater>(localMesh, zoneManager, rebuildThreshold);
        }

        if (!resultCache)
        {
            resultCache = std::make_unique<WallDistanceCache>(cacheValidityTime);
        }

        if (!inheritanceManager)
        {
            inheritanceManager = std::make_unique<TimeStepInheritanceManager>();
        }

        if (!loadBalancer)
        {
            loadBalancer = std::make_unique<DynamicLoadBalancer>(nProcessor);
        }

        // 初始化位置信息
        previousPositions.resize(localMesh->GetElementNumberReal());
        currentDisplacements.resize(localMesh->GetElementNumberReal());
        elementDisplacements.resize(localMesh->GetElementNumberReal());

        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); ++elemID)
        {
            previousPositions[elemID] = localMesh->GetElement(elemID).GetCenter();
            currentDisplacements[elemID] = Vector(0.0, 0.0, 0.0);
            elementDisplacements[elemID] = 0.0;
        }

        dynamicMetrics = DynamicWallDistanceMetrics{};
    }

    void WallDistanceCalculator::UpdateMeshMotion(const std::vector<Vector> &displacementField,
                                              double timeStep, int stepNumber)
    {
        if (!isDynamicMeshEnabled)
        {
            return;
        }

        currentTimeStep = timeStep;
        currentStepNumber = stepNumber;

        // 更新位移信息
        if (displacementField.size() >= localMesh->GetElementNumberReal())
        {
            for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); ++elemID)
            {
                currentDisplacements[elemID] = displacementField[elemID];
            }
            CalculateElementDisplacements(displacementField);
        }

        // 更新动态管理器状态
        if (dynamicManager)
        {
            // dynamicManager->updateMotionInfo(displacementField, timeStep, stepNumber);
        }

        // 分析运动模式
        MotionType motionType = AnalyzeMotionType(displacementField);

        // 根据运动模式调整策略
        if (incrementalUpdater)
        {
            // incrementalUpdater->setMotionType(motionType);
        }
    }

    bool WallDistanceCalculator::IncrementalUpdateWallDistances(bool forceFullUpdate)
    {
        if (!isDynamicMeshEnabled)
        {
            // 非动态模式，执行完全更新
            CalculateWallDistances();
            return false;
        }

        auto startTime = std::chrono::high_resolution_clock::now();

        bool success = false;
        if (forceFullUpdate || !incrementalUpdater)
        {
            // 执行完全重建
            CalculateWallDistances();
            dynamicMetrics.fullRebuilds++;
            success = false;
        }
        else
        {
            // 尝试增量更新
            // success = incrementalUpdater->performIncrementalUpdate(wallDistances, nearestWallFaceID);
            if (success)
            {
                dynamicMetrics.incrementalUpdates++;
            }
            else
            {
                // 增量更新失败，执行完全重建
                CalculateWallDistances();
                dynamicMetrics.fullRebuilds++;
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        double updateTime = std::chrono::duration<double>(endTime - startTime).count();
        dynamicMetrics.totalUpdateTime += updateTime;

        return success;
    }

    WallDistanceCalculator::MotionType WallDistanceCalculator::AnalyzeMotionType(const std::vector<Vector> &displacements) const
    {
        if (displacements.empty())
        {
            return RIGID_BODY;
        }

        // 计算位移的统计信息
        Vector avgDisplacement(0.0, 0.0, 0.0);
        Scalar maxDisplacement = 0.0;
        Scalar minDisplacement = INF;

        for (const auto &disp : displacements)
        {
            avgDisplacement += disp;
            Scalar mag = disp.Mag();
            maxDisplacement = std::max(maxDisplacement, mag);
            minDisplacement = std::min(minDisplacement, mag);
        }
        avgDisplacement /= displacements.size();

        // 计算位移的方差
        Scalar variance = 0.0;
        for (const auto &disp : displacements)
        {
            Vector diff = disp - avgDisplacement;
            // variance += diff.Mag2();
        }
        variance /= displacements.size();

        // 判断运动类型
        // Scalar relativeVariance = variance / (avgDisplacement.Mag2() + 1e-12);
        // if (relativeVariance < 0.1)
        // {
        //     return RIGID_BODY; // 刚体运动
        // }
        // else if (relativeVariance > 0.5)
        // {
        //     return DEFORMATION; // 变形运动
        // }
        // else
        // {
        //     return MIXED; // 混合运动
        // }
    }

    void WallDistanceCalculator::CalculateElementDisplacements(const std::vector<Vector> &displacements)
    {
        Scalar totalDisplacement = 0.0;
        Scalar maxDisp = 0.0;

        for (int elemID = 0; elemID < localMesh->GetElementNumberReal() && elemID < displacements.size(); ++elemID)
        {
            elementDisplacements[elemID] = displacements[elemID].Mag();
            totalDisplacement += elementDisplacements[elemID];
            maxDisp = std::max(maxDisp, elementDisplacements[elemID]);
        }

        // 更新统计信息
        dynamicMetrics.averageDisplacement = totalDisplacement / localMesh->GetElementNumberReal();
        dynamicMetrics.maxDisplacement = maxDisp;
    }

    Vector WallDistanceCalculator::CalculateVelocity(int elemID) const
    {
        if (currentTimeStep > 0.0 && elemID < currentDisplacements.size())
        {
            return currentDisplacements[elemID] / currentTimeStep;
        }
        return Vector(0.0, 0.0, 0.0);
    }

    void WallDistanceCalculator::SmartWallDistanceCalculation()
    {
        if (!isDynamicMeshEnabled)
        {
            // 非动态模式，直接计算
            CalculateWallDistances();
            return;
        }

        auto startTime = std::chrono::high_resolution_clock::now();

        // 第一步：尝试从缓存获取结果
        std::vector<int> elementsNeedingCalculation;
        for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); ++elemID)
        {
            Vector currentPos = localMesh->GetElement(elemID).GetCenter();
            List<Scalar> cachedDistances;
            List<int> cachedFaceIDs;

            // if (resultCache && resultCache->tryGetCachedResult(elemID, currentPos, cachedDistances, cachedFaceIDs))
            // {
            //     // 缓存命中，使用缓存结果
            //     for (int zoneID = 0; zoneID < n_Zones && zoneID < cachedDistances.size(); ++zoneID)
            //     {
            //         if (elemID < wallDistances[zoneID].size())
            //         {
            //             wallDistances[zoneID][elemID] = cachedDistances[zoneID];
            //         }
            //         if (elemID < nearestWallFaceID[zoneID].size())
            //         {
            //             nearestWallFaceID[zoneID][elemID] = cachedFaceIDs[zoneID];
            //         }
            //     }
            //     dynamicMetrics.cacheHits++;
            // }
            // else
            // {
            //     // 第二步：尝试从前一时间步继承
            //     Vector velocity = CalculateVelocity(elemID);
            //     List<Scalar> predictedDistances;
            //     List<int> predictedFaceIDs;

            //     if (inheritanceManager &&
            //         inheritanceManager->predictFromPreviousStep(elemID, currentPos, velocity, currentTimeStep,
            //                                                     predictedDistances, predictedFaceIDs))
            //     {
            //         // 继承成功，使用预测结果
            //         for (int zoneID = 0; zoneID < n_Zones && zoneID < predictedDistances.size(); ++zoneID)
            //         {
            //             if (elemID < wallDistances[zoneID].size())
            //             {
            //                 wallDistances[zoneID][elemID] = predictedDistances[zoneID];
            //             }
            //             if (elemID < nearestWallFaceID[zoneID].size())
            //             {
            //                 nearestWallFaceID[zoneID][elemID] = predictedFaceIDs[zoneID];
            //             }
            //         }
            //         dynamicMetrics.inheritanceHits++;
            //     }
            //     else
            //     {
            //         // 需要重新计算
            //         elementsNeedingCalculation.push_back(elemID);
            //         dynamicMetrics.cacheMisses++;
            //     }
            // }
        }

        // 第三步：对需要重新计算的单元执行实际计算
        if (!elementsNeedingCalculation.empty())
        {
            PerformActualWallDistanceCalculation(elementsNeedingCalculation);
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        double calculationTime = std::chrono::duration<double>(endTime - startTime).count();
        dynamicMetrics.totalUpdateTime += calculationTime;

        // 更新负载均衡信息
        if (loadBalancer)
        {
            // loadBalancer->updateProcessorLoad(processorID, elementsNeedingCalculation.size(), calculationTime);
        }
    }

    void WallDistanceCalculator::PerformActualWallDistanceCalculation(const std::vector<int> &elementsToUpdate)
    {
        if (elementsToUpdate.empty())
        {
            return;
        }

        // 为需要更新的单元计算壁面距离
        for (int zoneID = 0; zoneID < n_Zones; zoneID++)
        {
            if (globalWallFaceVectors[zoneID].empty())
            {
                // 子域没有壁面，设置为极大值
                for (int elemID : elementsToUpdate)
                {
                    if (elemID < wallDistances[zoneID].size())
                    {
                        wallDistances[zoneID][elemID] = INF;
                        nearestWallFaceID[zoneID][elemID] = -1;
                    }
                }
            }
            else
            {
                // 使用KDT搜索计算指定单元的壁面距离
                KDT kdtSearcher(dim == Mesh::MeshDim::md2D ? 2 : 3);
                kdtSearcher.CreateNodeKDTree(globalWallFaceVectors[zoneID]);

                for (int elemID : elementsToUpdate)
                {
                    if (elemID < wallDistances[zoneID].size())
                    {
                        Vector elemCenter = localMesh->GetElement(elemID).GetCenter();
                        auto nearest = kdtSearcher.SearchNearestNode(elemCenter);
                        wallDistances[zoneID][elemID] = nearest.first;
                        nearestWallFaceID[zoneID][elemID] = nearest.second;

                        // 缓存结果
                        if (resultCache)
                        {
                            List<Scalar> distances(n_Zones);
                            List<int> faceIDs(n_Zones);
                            for (int z = 0; z < n_Zones; ++z)
                            {
                                distances[z] = wallDistances[z][elemID];
                                faceIDs[z] = nearestWallFaceID[z][elemID];
                            }
                            // resultCache->cacheResult(elemID, distances, faceIDs, elemCenter);
                        }
                    }
                }
            }
        }

        // 修正壁面距离
        CorrectWallDistImproved();
    }

    // ==================== 壁面内外判断方法实现 ====================

    bool WallDistanceCalculator::IsPointInsideWallRayCasting(const Vector &point, int zoneID)
    {
        // 射线投射法的简化实现
        // 实际实现需要更复杂的几何计算
        return false; // 暂时返回false，表示在壁面外部
    }

    bool WallDistanceCalculator::IsPointInsideWallImprovedNormal(const Vector &point,
                                                             const Face &nearestFace,
                                                             const std::vector<Node> &faceNodes,
                                                             bool isSameZone)
    {
        // 改进法向量法的简化实现
        Vector faceCenter = nearestFace.GetCenter();
        Vector faceNormal = nearestFace.GetNormal();
        Vector pointToFace = faceCenter - point;

        // 计算点到面的法向距离
        Scalar normalDistance = pointToFace & faceNormal.Normalize();

        // 如果是同一子域，使用法向量判断
        if (isSameZone)
        {
            return normalDistance < 0; // 负值表示在壁面内部
        }
        else
        {
            // 不同子域，需要更复杂的判断
            return false; // 暂时返回false
        }
    }

    bool WallDistanceCalculator::IsPointInsideWallTopology(const Vector &point, int zoneID)
    {
        // 拓扑连通性法的简化实现
        // 实际实现需要分析网格拓扑结构
        return false; // 暂时返回false
    }

    bool WallDistanceCalculator::IsPointInsideWallHybrid(const Vector &point,
                                                     const Face &nearestFace,
                                                     const std::vector<Node> &faceNodes,
                                                     int zoneID,
                                                     int elemZoneID)
    {
        // 混合方法：结合多种判断方法
        bool normalResult = IsPointInsideWallImprovedNormal(point, nearestFace, faceNodes, zoneID == elemZoneID);
        bool rayCastingResult = IsPointInsideWallRayCasting(point, zoneID);
        bool topologyResult = IsPointInsideWallTopology(point, zoneID);

        // 简单的投票机制
        int insideCount = 0;
        if (normalResult)
            insideCount++;
        if (rayCastingResult)
            insideCount++;
        if (topologyResult)
            insideCount++;

        return insideCount >= 2; // 多数投票
    }

} // namespace Overset

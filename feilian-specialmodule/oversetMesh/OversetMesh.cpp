﻿#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

namespace Overset
{
OversetMesh::OversetMesh(Package::FlowPackage &flowPackage_)
	: flowPackage(flowPackage_),
	  flowConfig(flowPackage_.GetFlowConfigure()),
	  localMesh(flowPackage_.GetMeshStruct().mesh),
	  commitedAcceptors(localMesh->GetOversetRegion().GetAcceptorList()),
	  commitedDonors(localMesh->GetOversetRegion().GetDonorList()),
	  interpolationType(flowConfig.GetOverset().interpolationType),
	  elemTypeField(flowPackage_.GetField().oversetElemType),
	  isDynamicMeshEnabled(false),
	  rebuildThreshold(0.1),
	  cacheValidityTime(1.0),
	  currentTimeStep(0.0),
	  currentStepNumber(0)
{
	try
	{
		this->Initialize();

		// 初始化重构后的模块 - 使用异常安全的智能指针
		wallDistanceCalculator = UniquePtr<WallDistanceCalculator>(
			localMesh, zoneManager, flowConfig, mpi_world);
		donorSearcher = UniquePtr<DonorSearcher>(
			localMesh, zoneManager, elemTypeField, mpi_world);

		this->CreateNodeNeiElem();

		if (localOversetPatchID != -1)
		{
			this->GetOversetPatchElemID();
		}

		// 		//     用于并行调试
		// #if defined(_BaseParallelMPI_)
		// 	{
		// 		int i = 0;
		// 		std::cout << "Process" + ToString(GetMPIRank()) + " is sleeping......" << std::endl;
		// 		while (i == 0)
		// 		{
		// 			sleep(1);
		// 		}
		// 	}
		// #endif
	}
	catch (const std::exception &e)
	{
		throw std::runtime_error("OversetMesh构造失败: " + std::string(e.what()));
	}
	catch (...)
	{
		throw std::runtime_error("OversetMesh构造失败: 未知异常");
	}
}

OversetMesh::~OversetMesh()
{
}

void OversetMesh::InitOGA()
{
	//******************************************************
	if (processorID == 0)
		Print(" ");
	if (processorID == 0)
		Print(ObtainInfoTitle("重叠网格初始化组装"));

	this->CheckInputs();
	SystemTime oversetTime;

	//******************************************************
	if (processorID == 0)
		Print(" ");
	if (processorID == 0)
		Print("--> 初始化贡献单元搜索器······");

	SystemTime elemKDTtime;
	donorSearcher->Initialize();
	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("初始化贡献单元搜索器耗时： ", elemKDTtime);
	}

	// //测试全局搜索时间
	// Set<int> elemIDs;
	// List<List<Acceptor>> groupedAcpts;
	// for (int i = 0; i < localMesh->GetElementNumberReal(); i++)
	// {
	// 	elemIDs.insert(i);
	// }
	// MPIBarrier();
	// SystemTime donorSearchTime1;
	// this->GroupingAcceptors(elemIDs, groupedAcpts);
	// MPIBarrier();
	// if (processorID == 0 && verboseInfo){ PrintProcessTime("全局搜索耗时： ", donorSearchTime1); }
	// SystemTime donorSearchTime;
	// Set<Acceptor> searchResults;
	// this->ParallelDonorSearch(groupedAcpts,searchResults);
	// MPIBarrier();
	// if (processorID == 0 && verboseInfo){ PrintProcessTime("全局搜索耗时： ", donorSearchTime); }

	//******************************************************

	if (processorID == 0)
		Print(" ");
	if (processorID == 0)
		Print("--> 计算网格单元到各个子域的壁面距离······");

	SystemTime wallDistTime;
	this->CalculateWallDistances();
	// 清理贡献单元搜索器的标记，因为壁面距离修正时可能会进行搜索
	const List<int> &donorFlags = donorSearcher->GetAcceptorDonorFlag();
	acceptorDonorFlag.assign(donorFlags.begin(), donorFlags.end());
	acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0);
	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("壁面距离计算耗时： ", wallDistTime);
	}
	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 重叠网格单元类型判定······");

	SystemTime judgeTime;
	this->FindBackgroundZone();
	this->JudgeElemType();

	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("判定网格单元类型耗时： ", judgeTime);
	}

	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 构造并优化插值边界······");

	SystemTime advanceTime;
	this->CreateInitialFringe(stagedAcpts); // 创建初始插值单元，加入暂存容器
	this->AdvanceFringe();
	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("重叠边界推进耗时： ", advanceTime);
	}

	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 最终插值边界检查与改进······");
	this->CheckAndImproveFinalFringe(stagedAcpts);

	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 构造贡献单元及权重······");
	SystemTime donorTime;
	// 将暂存的Acceptor按照贡献单元的进程编号分组
	this->GroupingAcceptors(stagedAcpts, commitedAcceptors);
	this->CreateCommitedDonors();
	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("构造贡献单元及权重耗时： ", donorTime);
	}

	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 更新壁面距离······");
	this->UpdateWallDistField();

	//******************************************************
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("--> 更新网格计算域·····");
	this->UpdateElementTypeToMultiGrid();

	//******************************************************
	MPIBarrier();
	if (processorID == 0 && verboseInfo)
	{
		PrintProcessTime("重叠网格装配耗时： ", oversetTime);
	}
	if (GetMPIRank() == 0)
		Print(" ");
	if (GetMPIRank() == 0)
		Print("---------------完成重叠网格初始化装配-------------------");

	// ElementField<Vector> *v = flowPackage.GetField().velocity;
	// for (int i = 0; i < localMesh->GetElementNumberReal(); i++)
	// {
	// 	Scalar wallDist = localMesh->GetNearWallDistance(i);
	// 	// p->SetValue(i,i);
	// 	// t->SetValue(i,processorID);
	// 	Vector temp(i, wallDistances[0][i],wallDistances[1][i]);
	// 	v->SetValue(i,temp);
	// }
}

void OversetMesh::UpdateOGA()
{
	commitedAcceptors.clear();
	commitedDonors.clear();
	nodeOversetType.clear();
	nodeOversetType.clear();
	globalWallFaces.clear();
	wallDistances.clear();
	nearestWallFaceID.clear();

	for (int i = 0; i < globalWallFaceKdtTrees.size(); i++)
	{
		delete globalWallFaceKdtTrees[i];
	}
	globalWallFaceKdtTrees.clear();
	// globalElemKdtTrees.clear();

	this->InitOGA();
}

void OversetMesh::JudgeElemType()
{
	switch (elemTypeMethod)
	{
	case ElemTypeMethod::ElemBased:
		this->JudgeElemTypeElemBased();
		break;
	case ElemTypeMethod::NodeBased:
		// 先判断网格点类型
		this->JudgeNodeType();
		// 然后判断网格单元类型
		this->JudgeElemTypeNodeBased();
		break;
	default:
		break;
	}
}

void OversetMesh::JudgeElemTypeElemBased()
{
	// 先默认全部设定为计算单元
	elemTypeField->Initialize(OversetElemType::CALCULATED);

	int startElemID = zoneManager->GetZoneStartElemID(BGzoneID);
	int elemNum = zoneManager->GetZoneElemNum(BGzoneID);
	Set<int> inOversetBCelemID;
	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		const int &elemNum = zoneManager->GetZoneElemNum(zoneID);
		if (elemNum <= 0)
		{
			continue;
		} // 该子域在当前进程无网格

		if (zoneID == BGzoneID) // 背景网格
		{
			for (int elemID = startElemID; elemID < startElemID + elemNum; elemID++)
			{
				// 最小壁面距离非自身子域时，可能为洞单元
				if (!IsNearestWallDistToSelf(elemID, BGzoneID))
				{
					// 进一步判断是否在重叠边界内：
					//	a. 是：洞单元;
					//  b. 否：保持默认的计算单元;
					const Vector &elemCenter = localMesh->GetElement(elemID).GetCenter();
					if (NodeInOversetBC(elemCenter)) // 此处的判定并不严谨，可能存在个别情况下elemCenter实际上并不在重叠边界内，但被误判为在
					{
						inOversetBCelemID.insert(elemID); // 需要进一步判断
					}
				}
			}
		}
		else // 非背景子域网格
		{
			int startElemID = zoneManager->GetZoneStartElemID(zoneID);
			int elemNum = zoneManager->GetZoneElemNum(zoneID);
			for (int elemID = startElemID; elemID < startElemID + elemNum; elemID++)
			{
				// 最小壁面距离非自身子域时，为洞单元
				if (!IsNearestWallDistToSelf(elemID, zoneID))
				{
					elemTypeField->SetValue(elemID, OversetElemType::HOLE);
				}
			}
		}
	}

	// 根据贡献单元搜索进一步判断
	List<List<Acceptor>> groupedAcpts;
	Set<Acceptor> searchResults;
	this->GroupingAcceptors(inOversetBCelemID, groupedAcpts);
	this->ParallelDonorSearch(groupedAcpts, searchResults);
	for (auto it = searchResults.begin(); it != searchResults.end(); it++)
	{
		const int &elemID = it->GetAcceptorID();
		const int &donorID = it->GetCentralDonorID();
		if (donorID >= 0) // 有贡献单元，确认在重叠边界内部
		{
			elemTypeField->SetValue(elemID, OversetElemType::HOLE);
		}
		else // 没有贡献单元且在物面内部
		{
			for (int zoneI = 0; zoneI < n_Zones; zoneI++)
			{
				if (wallDistances[zoneI][elemID] < 0)
				{
					elemTypeField->SetValue(elemID, OversetElemType::HOLE);
				}
			}
		}
	}
	acceptorDonorFlag.assign(localMesh->GetElementNumberReal(), 0); // 贡献单元搜索可能会标记一些flag，但此时还不需要标记，此处清除一次，TODO：改进逻辑

	// 更新并行边界处虚单元的重叠类型
	elemTypeField->SetGhostlValueParallel();
	MPIBarrier(); // TODO:现在背景域判断时间与其他域的耗时太不均衡，还需要进一步优化
}

void OversetMesh::FindBackgroundZone()
{
	List<Scalar> zoneSpaceRange(n_Zones);
	List<List<Scalar>> zoneSpaceMin(n_Zones);
	List<List<Scalar>> zoneSpaceMax(n_Zones);

	int spaceDim = globalTreeInfo[0].spaceMax.size();
	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		zoneSpaceMin[zoneID].resize(spaceDim, INF);
		zoneSpaceMax[zoneID].resize(spaceDim, -1 * INF);
	}

	for (int i = 0; i < globalTreeInfo.size(); i++)
	{
		int &zoneID = globalTreeInfo[i].zoneID;
		for (int dimI = 0; dimI < spaceDim; dimI++)
		{
			zoneSpaceMin[zoneID][dimI] = std::min(zoneSpaceMin[zoneID][dimI], globalTreeInfo[i].spaceMin[dimI]);
			zoneSpaceMax[zoneID][dimI] = std::max(zoneSpaceMax[zoneID][dimI], globalTreeInfo[i].spaceMax[dimI]);
		}
	}

	for (int zoneID = 0; zoneID < n_Zones; zoneID++)
	{
		zoneSpaceRange[zoneID] = 0;
		for (int dimI = 0; dimI < spaceDim; dimI++)
		{
			zoneSpaceRange[zoneID] += zoneSpaceMax[zoneID][dimI] - zoneSpaceMin[zoneID][dimI];
		}
	}

	List<Scalar>::iterator biggest = std::max_element(zoneSpaceRange.begin(), zoneSpaceRange.end());
	BGzoneID = std::distance(zoneSpaceRange.begin(), biggest);

	if (processorID == 0 && verboseInfo)
	{
		Print("背景网格编号为： " + ToString(BGzoneID));
	}
}

void OversetMesh::JudgeNodeType()
{
	// 清空并开辟网格点的贡献单元信息列表
	nodeOversetType.clear();
	nodeOversetType.clear();
	nodeOversetType.resize(localMesh->GetNodeNumber());

	for (int nodeID = 0; nodeID < localMesh->GetNodeNumber(); nodeID++)
	{
		// const Vector &node = localMesh->GetNode(nodeID);
		nodeOversetType[nodeID] = OversetElemType::CALCULATED; // 先默认设定为计算节点

		for (int zoneID = 0; zoneID < n_Zones; zoneID++)
		{
			// 基于壁面距离进行一次判定, 对自身所在子域的壁面距离大于对其他子域的壁面距离时，有可能为洞点
			if (zoneID != myZoneID && wallDistances[myZoneID][nodeID] > wallDistances[zoneID][nodeID])
			{
				// 基于贡献单元搜索进行二次判定：
				// 1.不存在贡献单元时，判断是否在物面内部：
				//    a.在物面内部为洞点；
				//    b.不在物面内部为计算点
				// 2.存在贡献单元时均判定为洞点；
				bool foundDonor = true; // = FindDonor(nodeID, node, nodeOversetMap);
				if (!foundDonor)
				{
					bool inWall = NodeInWall(nodeID, zoneID);
					if (inWall)
					{
						nodeOversetType[nodeID] = OversetElemType::HOLE;
					}
				}
				if (foundDonor)
				{
					nodeOversetType[nodeID] = OversetElemType::HOLE;
				}
			}
		}
	}
}

void OversetMesh::JudgeElemTypeNodeBased()
{
	// 基于网格点的类型进行网格单元类型判定：
	//     1.单元的所有点均为洞点时判定为洞单元
	//     2.所有点均为计算节点时判定为计算单元
	//     3.其余判定为初始的插值单元，后续还需进行优化
	for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
	{
		// 先默认设置为计算单元；
		localMesh->v_elem[elemID].et_type = Element::real;

		int flag = 0;
		std::vector<int> &nodeIDList = localMesh->v_elem[elemID].v_nodeID;
		for (int i = 0; i < nodeIDList.size(); i++)
		{
			if (nodeOversetType[nodeIDList[i]] == OversetElemType::CALCULATED)
			{
				flag += 1;
			}
			else if (nodeOversetType[nodeIDList[i]] == OversetElemType::HOLE)
			{
				flag += -1;
			}
		}
		if (flag == nodeIDList.size())
		{
			localMesh->v_elem[elemID].et_type = Element::real;
		}
		else if (flag == nodeIDList.size() * -1)
		{
			localMesh->v_elem[elemID].et_type = Element::unavailable;
		}
		else // 初始插值单元
		{
			localMesh->v_elem[elemID].et_type = Element::ghostOverset;
			// globalOversetMap[processorID][elemID] = DonorInfo(-1, -1); // 由于初始插值单元都要向内推进，因此暂时先填充虚假的贡献单元信息
		}
	}
}

void OversetMesh::CreateInitialFringe(Set<Acceptor> &initialFringe)
{
	// 循环所有内部面，找到与计算单元 面相邻 的洞单元编号，加入初始阵面
	Set<int> fringeElemID;
	for (int faceI = 0; faceI < localMesh->GetFaceNumber(); faceI++)
	{
		if (localMesh->JudgeBoundaryFace(faceI))
		{
			continue;
		}

		int ownerID = localMesh->GetFace(faceI).GetOwnerID();
		int neiID = localMesh->GetFace(faceI).GetNeighborID();
		int ownerType = elemTypeField->GetValue(ownerID);
		int neiType = elemTypeField->GetValue(neiID);

		if (ownerType == OversetElemType::CALCULATED && neiType == OversetElemType::HOLE)
		{
			// Set<int> nodeNeiHole;
			// this->GetNodeNeighbourHole(localMesh->GetFace(faceI), nodeNeiHole);
			// this->GetFaceNeighbourHole(localMesh->GetFace(faceI), nodeNeiHole);
			// fringeElemID.insert(nodeNeiHole.begin(), nodeNeiHole.end());
			fringeElemID.insert(ownerID);
		}
		else if (ownerType == OversetElemType::HOLE && neiType == OversetElemType::CALCULATED)
		{
			// Set<int> nodeNeiHole;
			// this->GetNodeNeighbourHole(localMesh->GetFace(faceI), nodeNeiHole);
			// this->GetFaceNeighbourHole(localMesh->GetFace(faceI), nodeNeiHole);
			// fringeElemID.insert(nodeNeiHole.begin(), nodeNeiHole.end());
			fringeElemID.insert(neiID);
		}
	}
	// 将与重叠边界相邻的计算单元加入初始插值单元列表
	this->MarkElemAtOversetBoundary(fringeElemID);

	// 移除虚单元
	for (auto it = fringeElemID.begin(); it != fringeElemID.end();)
	{
		if (*it >= localMesh->GetElementNumberReal())
		{
			it = fringeElemID.erase(it);
		}
		else
		{
			it++;
		}
	}

	// 根据初始插值单元列表设定相应的场值
	this->SetAcceptorFieldValue(fringeElemID);

	// 为初始插值单元构造Acceptor，并加入暂存列表
	for (auto it = fringeElemID.begin(); it != fringeElemID.end(); it++)
	{
		this->StageOneAcceptor(*it);
	}

	// // 为初始插值单元搜索贡献单元
	// List<List<Acceptor>> groupedAcpts;
	// this->GroupingAcceptors(fringeElemID, groupedAcpts);
	// this->ParallelDonorSearch(groupedAcpts, initialFringe);

	// 优化初始插值边界，现在允许初始插值边界中存在贡献单元为Hole的情况
	// this->OptimizeInitialFringe();

	// 删去不与任何洞单元相邻的冗余插值单元
	this->ModifyRedundantAcceptor(stagedAcpts);

	// 更新并行边界处虚单元的重叠类型
	elemTypeField->SetGhostlValueParallel();

	fringeElemID.clear();
}

void OversetMesh::MarkElemAtOversetBoundary(Set<int> &fringeElemID)
{
	// 当前进程不存在重叠边界时直接返回
	if (localOversetPatchID < 0)
	{
		return;
	}
	// 将与重叠边界面相邻、且为计算单元的单元标记为插值单元
	for (Set<int>::iterator it = oversetPatchElemID.begin(); it != oversetPatchElemID.end(); it++)
	{
		const int &elemType = elemTypeField->GetValue(*it);
		if (elemType == OversetElemType::CALCULATED)
		{
			fringeElemID.insert(*it);
		}
	}
}

void OversetMesh::AdvanceFringe()
{
	// 将初始插值单元阵面向洞单元区域推进
	for (advanceNum = 0; advanceNum < maxAdvanceNum; advanceNum++)
	{
		// 获取与当前stagedAcceptors点相邻的所有洞单元编号
		Set<int> neiHoleID;
		this->GetNodeNeighbourHole(stagedAcpts, neiHoleID);
		// this->GetFaceNeighbourHole(stagedAcpts, neiHoleID);

		// 将相邻洞单元根据要并行搜索的进程号进行分组，并创建成初始Acceptor对象，放入暂存容器
		List<List<Acceptor>> groupedAcpts;
		this->GroupingAcceptors(neiHoleID, groupedAcpts);

		// 并行搜索贡献单元
		Set<Acceptor> neiAcpts;
		this->ParallelDonorSearch(groupedAcpts, neiAcpts);

		// 根据搜索结果将stagedAcceptors推进至下一层
		// this->ForwardAdvance(forwardStagedAcpts, donorSearchResults);
		this->AdvanceOneLayer(neiAcpts);

		// 推进后，更新一次并行边界虚单元的重叠类型
		elemTypeField->SetGhostlValueParallel();
		// 去除冗余单元
		this->ModifyRedundantAcceptor(stagedAcpts);

		// 贡献单元的类型可能已经随着推进而改变，更新一次贡献单元搜索
		this->GroupingAcceptors(stagedAcpts, groupedAcpts);
		this->ParallelDonorSearch(groupedAcpts, stagedAcpts);

		if (processorID == 0 && verboseInfo)
		{
			Print("\t插值边界推进至第" + ToString(advanceNum + 1) + "层......");
		}

		// 判断当前层是否还需要推进
		int advanceFlag = 0;
		int advanceCount = 0;
		for (auto it = stagedAcpts.begin(); it != stagedAcpts.end(); it++)
		{
			if (it->GetCentralDonorType() < OversetElemType::CALCULATED)
			{
				advanceFlag = 1;
			}
		}
		boost::mpi::all_reduce(mpi_world, advanceFlag, advanceCount, std::plus<int>());
		if (advanceCount == 0) // 所有进程都不再需要推进
		{
			break;
		}
	}
}

void OversetMesh::AdvanceOneLayer(Set<Acceptor> &neiLayerAcpts)
{
	Set<Acceptor> neiStagedAcpts;		// 相邻单元层中暂存的真实单元
	List<Acceptor> neiStagedAcptsGhost; // 相邻单元层中暂存的虚单元
	for (auto it = stagedAcpts.begin(); it != stagedAcpts.end(); it++)
	{
		const Acceptor &currentAcpt = *it;
		// 获取要推进的相邻层单元
		List<int> holeNeiElemID;
		List<Acceptor> neiAcpts;
		this->GetNodeNeighbourHole(currentAcpt.GetAcceptorID(), holeNeiElemID);
		// this->GetFaceNeighbourHole(currentAcpt, holeNeiElemID);
		for (int i = 0; i < holeNeiElemID.size(); i++)
		{
			const Acceptor &neiAcpt = this->GetDonorSearchResults(holeNeiElemID[i], neiLayerAcpts);
			neiAcpts.push_back(neiAcpt);
		}

		// 判断是否推进
		if (this->JudgeAdvance(currentAcpt, neiAcpts)) // 推进
		{
			// 暂存相邻单元
			for (int i = 0; i < neiAcpts.size(); i++)
			{
				this->StageOneAcceptor(neiAcpts[i], neiStagedAcpts, neiStagedAcptsGhost);
				elemTypeField->SetValue(neiAcpts[i].GetAcceptorID(), OversetElemType::ACCEPTOR);
			}
			// 处理当前单元
			const int &elemID = currentAcpt.GetAcceptorID();
			if (this->JudgeOversetPatchElem(elemID)) // 与重叠边界相邻，需要保持为插值单元，继续加入暂存列表
			{
				this->StageOneAcceptor(currentAcpt, neiStagedAcpts, neiStagedAcptsGhost);
			}
			else // 不与重叠边界相邻，设定为计算单元
			{
				elemTypeField->SetValue(elemID, OversetElemType::CALCULATED);
			}
		}
		else // 不再推进,但贡献单元信息还有可能需要更新，继续加入暂存列表
		{
			this->StageOneAcceptor(currentAcpt, neiStagedAcpts, neiStagedAcptsGhost);
		}
	}

	// 将虚单元推进为插值单元时，对应的真实单元进程接收数据并加入暂存列表
	this->AllGatherAndMergeList(neiStagedAcptsGhost);
	for (int i = 0; i < neiStagedAcptsGhost.size(); i++)
	{
		const int &acptProcID = neiStagedAcptsGhost[i].GetAcceptorProcID();
		const int &acptElemID = neiStagedAcptsGhost[i].GetAcceptorID();
		if (acptProcID == processorID)
		{
			this->StageOneAcceptor(neiStagedAcptsGhost[i], neiStagedAcpts, neiStagedAcptsGhost);
			elemTypeField->SetValue(neiStagedAcptsGhost[i].GetAcceptorID(), OversetElemType::ACCEPTOR);
		}
	}

	// 更新暂存列表
	stagedAcpts.swap(neiStagedAcpts);
}

bool OversetMesh::JudgeAdvance(const Acceptor &currentAcpt, const List<Acceptor> &neiAcpts)
{
	if (advanceNum < minAdvanceNum) // 推进层数在最小层数以下时必须推进
	{
		return true;
	}

	if (neiAcpts.size() == 0) // 无相邻单元，无法推进，直接返回false
	{
		return false;
	}

	// 统计相邻层单元的贡献单元重叠类型数量
	int acptCount = 0;
	int holeCount = 0;
	for (int i = 0; i < neiAcpts.size(); i++)
	{
		const int &neiDonorID = neiAcpts[i].GetCentralDonorID();
		const int &neiDonorType = neiAcpts[i].GetCentralDonorType();
		if (neiDonorID < 0) // 相邻单元存在无法找到贡献单元（在计算域外）的情况，无法推进
		{
			return false;
		}

		if (neiDonorType == OversetElemType::CALCULATED)
		{
		}
		else if (neiDonorType == OversetElemType::ACCEPTOR)
		{
			acptCount++;
		}
		else if (neiDonorType == OversetElemType::HOLE)
		{
			holeCount++;
		}
		else
		{
			throw std::runtime_error("OversetMesh: 未知的重叠单元类型，donorType=" + ToString(neiDonorType));
		}
	}

	// 判断当前单元与相邻单元的贡献单元情况，有四种情况：
	// a. 当前单元的贡献单元是Hole或Acceptor，且相邻洞单元的贡献单元均为Acceptor或计算单元：
	//    说明相邻层单元的贡献单元优于当前单元或持平，需要继续推进到更优区域
	// b. 当前单元的贡献单元是Calculated，且相邻单元的贡献单元也都是Calculated:
	//    检查当前单元是否也同时也作为了贡献单元（在贡献单元搜索时进行了标记，存储在acceptorDonorFlag中):
	//    b1:是，应当继续推进
	//	  b2:否，不再继续推进
	// c. 当前单元的贡献单元是Calculated，且相邻单元的贡献单元中包含Hole或Acceptor：
	//    说明相邻单元的贡献单元劣于当前单元，不再推进
	// d. 当前单元的贡献单元是Hole，且相邻单元的贡献单元也存在Hole:
	//    说明这两层尚未完全重叠，需要继续推进以增大重叠区域
	// 当前单元信息：
	const int &elemDonorType = currentAcpt.GetCentralDonorType();
	if (elemDonorType <= OversetElemType::ACCEPTOR && holeCount == 0) // 情况a
	{
		return true;
	}
	else if (elemDonorType == OversetElemType::CALCULATED)
	{
		if (holeCount == 0 && acptCount == 0) // 情况b
		{
			const int &elemID = currentAcpt.GetAcceptorID();
			if (acceptorDonorFlag[elemID] == 1) // 情况b1
			{
				acceptorDonorFlag[elemID] == 0; // 该单元会被推进，将标记置0
				return true;
			}
			else // 情况b2
			{
				return false;
			}

			// // Scalar elemVolumeRatio =  elemDonorVolume / elemVolume ; // 当前单元的体积比，
			// // 统计所有相邻单元是否大于贡献单元体积
			// // Scalar averagedVolumeRatio = 0;
			// int n = neiAcpts.size();
			// bool neiFlag = true;
			// for (int i = 0; i < n; i++)
			// {
			// 	const int &neiID = neiAcpts[i].GetAcceptorID();
			// 	const Scalar &neiVolume = localMesh->GetElement(neiID).GetVolume();
			// 	const Scalar &neiDonorVolume = neiAcpts[i].GetCentralDonorVolume();
			// 	if (neiVolume > neiDonorVolume)
			// 	{
			// 		neiFlag = false;
			// 	}
			//
			// 	// averagedVolumeRatio += neiDonorVolume / neiVolume ;
			// }
			// // averagedVolumeRatio = averagedVolumeRatio / n;
			//
			// // if (elemVolumeRatio > 2 && abs(elemVolumeRatio - 1) > abs(averagedVolumeRatio - 1))
			// if (elemVolume < elemDonorVolume && neiFlag == true)
			// {
			// 	return true;
			// }
			// else
			// {
			// 	return false;
			// }
		}
		else // 情况c
		{
			return false;
		}
	}
	else if (elemDonorType == OversetElemType::HOLE && holeCount > 0) // 情况d
	{
		return true;
	}
}

void OversetMesh::StageOneAcceptor(const int &elemID)
{
	const int &zoneID = zoneManager->GetElemZoneID(elemID);
	const Vector &center = localMesh->GetElement(elemID).GetCenter();
	Acceptor newAcceptor(elemID, processorID, zoneID, center);
	stagedAcpts.insert(newAcceptor);
}

void OversetMesh::StageOneAcceptor(const Acceptor &acpt, Set<Acceptor> &realList, List<Acceptor> &ghostList)
{
	const int &elemID = acpt.GetAcceptorID();
	if (localMesh->GetElement(elemID).GetElemType() != Element::ElemType::ghostParallel) // 非虚单元
	{
		realList.insert(acpt);
	}
	else // 并行虚单元
	{
		// 获取虚单元的真实所在进程和当地编号，修改Acceptor信息
		const List<List<GhostElement>> &ghostElem = localMesh->GetGhostElementsParallel();
		for (int i = 0; i < ghostElem.size(); i++)
		{
			for (int j = 0; j < ghostElem[i].size(); j++)
			{
				int ghostElemID = ghostElem[i][j].GetLocalIDPair().first;
				int realElemID = ghostElem[i][j].GetLocalIDPair().second;
				int realProcID = ghostElem[i][j].GetProcessorIDPair().second;
				if (elemID == ghostElemID)
				{
					Acceptor temp = acpt;
					temp.SetAcceptorProcID(realProcID);
					temp.SetAcceptorID(realElemID);
					ghostList.push_back(temp);
				}
			}
		}
	}
}

void OversetMesh::CommitOneAcceptor(Acceptor &acpt, List<List<Acceptor>> &commitAcptList)
{
	const int &acptID = acpt.GetAcceptorID();
	const int &donorProcID = acpt.GetCentralDonorProcID();
	commitAcptList[donorProcID].push_back(acpt);
}

void OversetMesh::CheckAndImproveFinalFringe(Set<Acceptor> &finalFringe)
{
	// 1. 对本进程插值单元的贡献类型进行检查并改进
	for (auto it = finalFringe.begin(); it != finalFringe.end(); ++it)
	{
		const int &elemID = it->GetAcceptorID();
		const int &elemZoneID = zoneManager->GetElemZoneID(elemID);
		const int &donorID = it->GetCentralDonorID();
		const int &donorType = it->GetCentralDonorType();

		if (donorID < 0) // 1. 对无贡献单元的插值单元进行报错
		{
			throw std::runtime_error("OversetMesh: zoneID " + ToString(elemZoneID) +
									 ", procID: " + ToString(processorID) + ", elemID " + ToString(elemID) +
									 " 无贡献单元信息！elemCenter: " + ToString(it->GetAcceptorCenter()));
		}
		if (donorType == OversetElemType::ACCEPTOR && verboseInfo) // 2. 对贡献单元为插值单元的进行警示但允许继续
		{
			if (processorID == 0)
			{
				Print("警告: OversetMesh: zoneID " + ToString(elemZoneID) +
					  " elemID " + ToString(elemID) + " 的贡献单元为插值单元！");
			}
		}
		if (donorID < 0 || donorType == OversetElemType::HOLE) // 3. 对贡献单元或贡献单元为洞单元的进行修正
		{
			this->ImproveWorstDonorQuality(*it);
		}
	}
}

void OversetMesh::ImproveWorstDonorQuality(const Acceptor &acpt)
{
	const int &elemID = acpt.GetAcceptorID();
	Set<int> acptNeiID;
	this->GetNodeNeighbourAcpt(elemID, acptNeiID);
	if (acptNeiID.size() == 0)
	{
		throw std::runtime_error("OversetMesh: 插值边界中的单元不与其他任何插值单元相邻! elemID=" + ToString(elemID));
	}
	for (auto it = acptNeiID.begin(); it != acptNeiID.end(); it++)
	{
		const int neiID = *it;
		if (neiID >= localMesh->GetElementNumberReal()) // 虚单元，略过
		{
			continue;
		}

		Acceptor temp(neiID, processorID, -1, {0, 0, 0}); // 创建一个临时插值单元用于搜索
		auto neiAcpt = stagedAcpts.find(temp);
		if (neiAcpt != stagedAcpts.end())
		{
			if (neiAcpt->GetCentralDonorType() >= 0)
			{
				acpt.SetCentralDonor(
					neiAcpt->GetCentralDonorID(),
					neiAcpt->GetCentralDonorProcID(),
					neiAcpt->GetCentralDonorVolume(),
					neiAcpt->GetCentralDonorType());
				return; // 找到一个可替代单元即返回，不再比较其他，TODO:后续可以视情况从多个可替代单元中选择最优的
			}
			else // 相邻插值单元的贡献单元也是洞单元，也一并递归的处理
			{
				// this->ImproveWorstDonorQuality(*neiAcpt);
			}
		}
		else
		{
			throw std::runtime_error("OversetMesh: 相邻的插值单元不在暂存插值边界中！procID: " +
									 ToString(processorID) + ", elemID: " + ToString(elemID) + ", neiID: " + ToString(neiID));
		}
	}

	if (acpt.GetCentralDonorID() < 0)
	{
		throw std::runtime_error("OversetMesh: 贡献单元为插值单元且没有相邻的插值单元替代！elemID=" + ToString(elemID));
	}
}

void OversetMesh::UpdateOversetField()
{
	// 重叠单元类型
	auto &oversetElemTypeField = *(flowPackage.GetField().oversetElemType);
	for (int elemID = 0; elemID < localMesh->n_elemNum; elemID++)
	{

		if (localMesh->v_elem[elemID].et_type == Element::unavailable)
		{
			oversetElemTypeField.SetValue(elemID, OversetElemType::HOLE);
		}
		else if (localMesh->v_elem[elemID].et_type == Element::ghostOverset)
		{
			oversetElemTypeField.SetValue(elemID, OversetElemType::ACCEPTOR);
		}
		else if (localMesh->v_elem[elemID].et_type == Element::real)
		{
			oversetElemTypeField.SetValue(elemID, OversetElemType::CALCULATED);
		}
	}
}

void OversetMesh::SetAcceptorFieldValue(Set<Acceptor> &acpts)
{
	for (auto it = acpts.begin(); it != acpts.end(); it++)
	{
		const int &elemID = it->GetAcceptorID();
		elemTypeField->SetValue(elemID, OversetElemType::ACCEPTOR);
	}
}

void OversetMesh::SetAcceptorFieldValue(Set<int> &elemIDlist)
{
	for (auto it = elemIDlist.begin(); it != elemIDlist.end(); it++)
	{
		elemTypeField->SetValue(*it, OversetElemType::ACCEPTOR);
	}
}

void OversetMesh::ModifyRedundantAcceptor(Set<Acceptor> &acpts)
{
	// Set<Acceptor> addAcpts;
	for (auto it = acpts.begin(); it != acpts.end();)
	{
		const int &elemID = it->GetAcceptorID();
		const bool &patchFlag = this->JudgeOversetPatchElem(elemID);
		List<int> nodeNeiCalc;
		List<int> nodeNeiHole;
		this->GetNodeNeighbourCalc(elemID, nodeNeiCalc);
		this->GetNodeNeighbourHole(elemID, nodeNeiHole);
		if (nodeNeiCalc.size() == 0 && nodeNeiHole.size() >= 0) // 只与插值单元或洞单元相邻，设为洞单元
		{
			elemTypeField->SetValue(elemID, OversetElemType::HOLE);
			it = acpts.erase(it);
		}
		else if (nodeNeiCalc.size() > 0 && nodeNeiHole.size() == 0) // 不与任何洞单元点相邻
		{
			if (patchFlag) // 与重叠边界相邻，保持为插值单元
			{
				it++;
				continue;
			}
			else // 不与重叠边界相邻，是冗余插值单元，设为计算单元
			{
				elemTypeField->SetValue(elemID, OversetElemType::CALCULATED);
				it = acpts.erase(it);
			}
		}
		else // 同时与计算单元和洞单元相邻
		{
			it++;
		}
	}
}

void OversetMesh::UpdateElemTypeFieldToMesh(ElementField<int> *field, Mesh *mesh)
{
	for (int elemID = 0; elemID < mesh->GetElementNumberReal(); elemID++)
	{
		const int &elemType = field->GetValue(elemID);
		if (elemType == OversetElemType::CALCULATED)
		{
			mesh->v_elem[elemID].et_type = Element::ElemType::real;
		}
		else if (elemType == OversetElemType::ACCEPTOR)
		{
			mesh->v_elem[elemID].et_type = Element::ElemType::ghostOverset;
		}
		else if (elemType == OversetElemType::HOLE)
		{
			mesh->v_elem[elemID].et_type = Element::ElemType::unavailable;
		}
		else
		{
			throw std::runtime_error("OversetMesh: 无效的重叠网格单元类型，elemType=" + ToString(elemType) + ", elemID=" + ToString(elemID));
		}
	}

	mesh->UpdateInDomainInfo();
}

void OversetMesh::UpdateElementTypeToMultiGrid()
{
	// 先更新细网格单元类型
	this->UpdateElemTypeFieldToMesh(elemTypeField, localMesh);

	// 基于细网格类型更新粗网格
	for (int level = 1; level < flowConfig.GetAcceleration().multigridSolver.level; level++)
	{
		MultiGrid *fineMesh = subMesh->GetMultiGrid(level - 1);
		MultiGrid *coarseMesh = subMesh->GetMultiGrid(level);
		ElementField<int> coarseMeshElemType(coarseMesh, OversetElemType::CALCULATED, "oversetElemType");

		const int IDSize = coarseMesh->GetFineToCoarseIDPairSize();
		for (int index = 0; index < IDSize; ++index)
		{
			const auto &IDPair = coarseMesh->GetFineToCoarseIDPair(index);
			const int &fineID = IDPair.first;
			const int &coarseID = IDPair.second;

			if (fineMesh->GetElement(fineID).GetElemType() == Element::ElemType::unavailable || fineMesh->GetElement(fineID).GetElemType() == Element::ElemType::ghostOverset)
			{
				coarseMeshElemType.SetValue(coarseID, OversetElemType::HOLE);
			}
		}
		coarseMeshElemType.SetGhostlValueParallel();

		// 循环粗网格内部面，将与洞单元相邻的计算单元标记为插值单元
		for (int faceI = 0; faceI < coarseMesh->GetFaceNumber(); faceI++)
		{
			if (coarseMesh->JudgeBoundaryFace(faceI))
			{
				continue;
			}

			int ownerID = coarseMesh->GetFace(faceI).GetOwnerID();
			int neiID = coarseMesh->GetFace(faceI).GetNeighborID();
			int ownerType = coarseMeshElemType.GetValue(ownerID);
			int neiType = coarseMeshElemType.GetValue(neiID);

			if (ownerType == OversetElemType::CALCULATED && neiType == OversetElemType::HOLE)
			{
				coarseMeshElemType.SetValue(ownerID, OversetElemType::ACCEPTOR);
			}
			else if (ownerType == OversetElemType::HOLE && neiType == OversetElemType::CALCULATED)
			{
				coarseMeshElemType.SetValue(neiID, OversetElemType::ACCEPTOR);
			}
		}
		this->UpdateElemTypeFieldToMesh(&coarseMeshElemType, coarseMesh);
	}
}

void OversetMesh::OptimizeInitialFringe()
{
	// 一、对无法搜索到贡献单元的初始插值单元（可能已经落在计算域外），向计算单元回退一层,并为其搜索贡献单元，如果还无法搜索到则报错
	Set<int> backwardElemID;
	for (auto it = stagedAcpts.begin(); it != stagedAcpts.end();)
	{
		const int &elemID = it->GetAcceptorID();
		const int &donorID = it->GetCentralDonorID();

		bool elemInWallFlag = false;
		for (int zoneID = 0; zoneID < n_Zones; zoneID++)
		{
			if (wallDistances[zoneID][elemID] < 0)
			{
				elemInWallFlag = true;
			}
		}

		if (elemInWallFlag && donorID < 0)
		{
			List<int> faceNeiCalc;
			this->GetFaceNeighbourCalc(elemID, faceNeiCalc); // 采用面相邻，因为点相邻会将过多的计算单元转为插值单元，后续贡献单元出问题的可能性会增大

			if (faceNeiCalc.size() > 0)
			{
				backwardElemID.insert(faceNeiCalc.begin(), faceNeiCalc.end()); // 将相邻计算单元加入反推
				it = stagedAcpts.erase(it);									   // 将当前单元从暂存插值边界中移除
				elemTypeField->SetValue(elemID, OversetElemType::HOLE);
			}
			else
			{
				const int &elemZoneID = zoneManager->GetElemZoneID(elemID);
				const int &elemGlobalID = subMesh->GetElementGlobalID(elemID);
				const Node &elemCenter = localMesh->GetElement(elemID).GetCenter();
				throw std::runtime_error("OversetMesh: 初始插值单元阵面无法找到贡献单元且无法反推: zoneID=" +
										 ToString(elemZoneID) + ", procID=" + ToString(processorID) +
										 ", elemID=" + ToString(elemID) + ", elemCenter=" + ToString(elemCenter));
				++it;
			}
		}
		else
		{
			++it;
		}
	}

	// 检查需要反推的单元中是否有虚单元，有虚单元时，由对应进程负责后续搜索
	List<std::pair<int, int>> realElemInfo; // 真实单元信息，pair<真实单元所在进程，真实单元的当地编号>
	for (auto it = backwardElemID.begin(); it != backwardElemID.end();)
	{
		if (*it >= localMesh->GetElementNumberReal())
		{
			// 获取虚单元的真实所在进程和当地编号
			const List<List<GhostElement>> &ghostElem = localMesh->GetGhostElementsParallel();
			for (int i = 0; i < ghostElem.size(); i++)
			{
				for (int j = 0; j < ghostElem[i].size(); j++)
				{
					int ghostElemID = ghostElem[i][j].GetLocalIDPair().first;
					int realElemID = ghostElem[i][j].GetLocalIDPair().second;
					int realProcID = ghostElem[i][j].GetProcessorIDPair().second;
					if (*it == ghostElemID)
					{
						realElemInfo.push_back(std::make_pair(realProcID, realElemID));
					}
				}
			}
			it = backwardElemID.erase(it);
		}
		else
		{
			++it;
		}
	}

	this->AllGatherAndMergeList(realElemInfo);
	for (int i = 0; i < realElemInfo.size(); i++)
	{
		if (realElemInfo[i].first == processorID)
		{
			backwardElemID.insert(realElemInfo[i].second);
		}
	}

	// 搜索贡献单元
	List<List<Acceptor>> groupedAcpts;
	Set<Acceptor> searchResults;
	this->GroupingAcceptors(backwardElemID, groupedAcpts);
	this->ParallelDonorSearch(groupedAcpts, searchResults);
	// 检查搜索结果
	for (auto it = searchResults.begin(); it != searchResults.end(); ++it)
	{
		const int &elemID = it->GetAcceptorID();
		const int &donorID = it->GetCentralDonorID();
		if (donorID < 0)
		{
			const Node &elemCenter = localMesh->GetElement(elemID).GetCenter();
			throw std::runtime_error("OversetMesh: 初始阵面反推时无法找到贡献单元: procID=" +
									 ToString(processorID) + ", elemID=" + ToString(elemID) + ", elemCenter=" + ToString(elemCenter));
		}
		else
		{
			stagedAcpts.insert(*it); // 加入暂存列表, TODO:需要测试是否存在反推到虚单元的情况
			elemTypeField->SetValue(elemID, OversetElemType::ACCEPTOR);
		}
	}
}

/**
 * 暂时不用的代码
 *
 */

void OversetMesh::DeletDonorStencil(int elemID)
{
	// globalOversetMap[processorID].erase(elemID);
}

void OversetMesh::FindParallelNeighbour(int &neiProcID, int &neiElemID)
{
	std::vector<std::vector<GhostElement>> pair = localMesh->GetGhostElementsParallel();

	for (int i = 0; i < pair.size(); i++)
	{
		for (int j = 0; j < pair[i].size(); j++)
		{
			if (pair[i][j].GetLocalIDPair().first == neiElemID)
			{
				neiProcID = pair[i][j].GetProcessorIDPair().second;
				neiElemID = pair[i][j].GetLocalIDPair().second;
				return;
			}
		}
	}
	throw std::runtime_error("OversetMesh::FindParallelNeighbour: 未找到并行的相邻单元信息，确认该单元是否与并行边界相邻，neiElemID=" + ToString(neiElemID));
}

void OversetMesh::ForwardAdvance(Set<Acceptor> &stagedAcpts, Set<Acceptor> &neiDonorSearchResults)
{
	Set<Acceptor> neiStagedAcpts;		// 相邻单元层中暂存的真实单元
	List<Acceptor> neiStagedAcptsGhost; // 相邻单元层中暂存的虚单元
	for (auto it = stagedAcpts.begin(); it != stagedAcpts.end(); it++)
	{
		const int &elemID = it->GetAcceptorID();
		// 获取相邻洞单元的贡献单元信息
		List<int> holeNeiElemID;
		this->GetNodeNeighbourHole(elemID, holeNeiElemID);
		List<Acceptor> neiAcpts;
		for (int i = 0; i < holeNeiElemID.size(); i++)
		{
			const Acceptor &neiAcpt = this->GetDonorSearchResults(holeNeiElemID[i], neiDonorSearchResults);
			neiAcpts.push_back(neiAcpt);
		}

		// 判断是否推进
		bool flag = this->JudgeAdvance(*it, neiAcpts);
		if (flag)
		{
			elemTypeField->SetValue(elemID, 10); // 预判定要推进时，场值先设为10
		}
		// if (advanceFlag == true) //需要推进
		// {
		// 	for (int i = 0; i < neiAcpts.size(); i++) // 暂存相邻单元到下一层容器
		// 	{
		// 		this->StageOneAcceptor(neiAcpts[i], neiStagedAcpts, neiStagedAcptsGhost);
		// 	}
		// 	// 处理当前单元
		// 	if (this->JudgeOversetPatchElem(elemID))// 与重叠边界相邻，需要保持为插值单元，直接加入到反向推进容器中
		// 	{
		// 		backwardStagedAcpts.insert(*it);
		// 	}
		// 	else // 不与重叠边界相邻，设定为计算单元
		// 	{
		// 		elemTypeField->SetValue(elemID, OversetElemType::CALCULATED);
		// 	}
		// }
		// else // 不需要推进，直接加入到反向推进容器中
		// {
		// 	backwardStagedAcpts.insert(*it);
		// }
	}

	for (auto it = stagedAcpts.begin(); it != stagedAcpts.end(); it++)
	{
		const int &elemID = it->GetAcceptorID();
		if (elemTypeField->GetValue(elemID) > 5)
		{
			// 获取相邻的插值单元
			Set<int> acptNeiElemID;
			this->GetNodeNeighbourAcpt(elemID, acptNeiElemID);
			int advanceCount = 0;
			for (auto acptIt = acptNeiElemID.begin(); acptIt != acptNeiElemID.end(); acptIt++)
			{
				if (elemTypeField->GetValue(*acptIt) > 5)
				{
					advanceCount++;
				}
			}

			if (advanceCount >= acptNeiElemID.size() / 2)
			{
				// 获取相邻洞单元的贡献单元信息
				List<int> holeNeiElemID;
				this->GetNodeNeighbourHole(elemID, holeNeiElemID);
				List<Acceptor> neiAcpts;
				for (int i = 0; i < holeNeiElemID.size(); i++)
				{
					const Acceptor &neiAcpt = this->GetDonorSearchResults(holeNeiElemID[i], neiDonorSearchResults);
					neiAcpts.push_back(neiAcpt);
				}
				for (int i = 0; i < neiAcpts.size(); i++) // 暂存相邻单元到下一层容器
				{
					this->StageOneAcceptor(neiAcpts[i], neiStagedAcpts, neiStagedAcptsGhost);
				}
				// 处理当前单元
				if (this->JudgeOversetPatchElem(elemID)) // 与重叠边界相邻，需要保持为插值单元，直接加入到反向推进容器中
				{
					backwardStagedAcpts.insert(*it);
					elemTypeField->SetValue(elemID, OversetElemType::ACCEPTOR);
				}
				else // 不与重叠边界相邻，设定为计算单元
				{
					elemTypeField->SetValue(elemID, OversetElemType::CALCULATED);
				}
				continue;
			}
			else
			{
				elemTypeField->SetValue(elemID, OversetElemType::ACCEPTOR);
			}
		}
		// 不需要推进，直接加入到反向推进容器中
		backwardStagedAcpts.insert(*it);
	}

	// 将虚单元推进为插值单元时，对应的真实单元进程接收数据并加入暂存列表
	this->AllGatherAndMergeList(neiStagedAcptsGhost);
	for (int i = 0; i < neiStagedAcptsGhost.size(); i++)
	{
		const int &acptProcID = neiStagedAcptsGhost[i].GetAcceptorProcID();
		const int &acptElemID = neiStagedAcptsGhost[i].GetAcceptorID();
		if (acptProcID == processorID)
		{
			this->StageOneAcceptor(neiStagedAcptsGhost[i], neiStagedAcpts, neiStagedAcptsGhost);
		}
	}
	// 修改相邻暂存列表的场值
	this->SetAcceptorFieldValue(neiStagedAcpts);
	// 更新暂存列表
	stagedAcpts.swap(neiStagedAcpts);
}

// ==================== 动态网格支持实现 ====================

void OversetMesh::EnableDynamicMesh(Scalar rebuildThreshold_, double cacheValidityTime_)
{
	isDynamicMeshEnabled = true;
	rebuildThreshold = rebuildThreshold_;
	cacheValidityTime = cacheValidityTime_;

	// 启用子模块的动态网格模式
	if (wallDistanceCalculator)
	{
		wallDistanceCalculator->EnableDynamicMesh(rebuildThreshold, cacheValidityTime);
	}

	if (donorSearcher)
	{
		donorSearcher->enableDynamicMesh(rebuildThreshold, cacheValidityTime);
	}

	// 初始化位置记录
	previousPositions.resize(localMesh->GetElementNumberReal());
	currentDisplacements.resize(localMesh->GetElementNumberReal());

	// 记录初始位置
	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		previousPositions[elemID] = localMesh->GetElement(elemID).GetCenter();
	}

	if (processorID == 0 && verboseInfo)
	{
		Print("动态网格模式已启用，重建阈值: " + ToString(rebuildThreshold) +
			  ", 缓存有效时间: " + ToString(cacheValidityTime) + "秒");
	}
}

void OversetMesh::UpdateMeshMotion(const std::vector<Vector> &displacementField,
								   double timeStep, int stepNumber)
{
	if (!isDynamicMeshEnabled)
	{
		return;
	}

	currentTimeStep = timeStep;
	currentStepNumber = stepNumber;

	// 更新位移场
	if (displacementField.size() >= localMesh->GetElementNumberReal())
	{
		for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
		{
			currentDisplacements[elemID] = displacementField[elemID];
		}
	}

	// 更新子模块的网格运动信息
	if (wallDistanceCalculator)
	{
		wallDistanceCalculator->UpdateMeshMotion(displacementField, timeStep, stepNumber);
	}

	if (donorSearcher)
	{
		donorSearcher->updateMeshMotion(displacementField, timeStep, stepNumber);
	}

	// 计算统计信息
	Scalar maxDisplacement = 0.0;
	Scalar totalDisplacement = 0.0;
	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		Scalar displacement = currentDisplacements[elemID].Magnitude();
		maxDisplacement = std::max(maxDisplacement, displacement);
		totalDisplacement += displacement;
	}

	dynamicMeshMetrics.maxDisplacement = maxDisplacement;
	dynamicMeshMetrics.averageDisplacement = totalDisplacement / localMesh->GetElementNumberReal();
}

bool OversetMesh::DynamicUpdateOGA(bool forceFullUpdate)
{
	if (!isDynamicMeshEnabled)
	{
		// 如果未启用动态网格，执行完全更新
		UpdateOGA();
		return false;
	}

	auto startTime = std::chrono::high_resolution_clock::now();

	bool incrementalSuccess = false;

	if (!forceFullUpdate)
	{
		// 尝试增量更新
		bool wallDistSuccess = wallDistanceCalculator->IncrementalUpdateWallDistances();
		bool donorSearchSuccess = true; // donorSearcher的增量更新在搜索过程中自动处理

		incrementalSuccess = wallDistSuccess && donorSearchSuccess;
	}

	if (!incrementalSuccess || forceFullUpdate)
	{
		// 增量更新失败或强制完全更新，执行完全重建
		if (processorID == 0 && verboseInfo)
		{
			Print("执行完全重叠网格装配重建...");
		}

		UpdateOGA();
		dynamicMeshMetrics.fullRebuilds++;
	}
	else
	{
		// 增量更新成功
		if (processorID == 0 && verboseInfo)
		{
			Print("增量更新重叠网格装配成功");
		}

		// 更新重叠网格单元类型场
		UpdateOversetField();
		dynamicMeshMetrics.incrementalUpdates++;
	}

	auto endTime = std::chrono::high_resolution_clock::now();
	double updateTime = std::chrono::duration<double>(endTime - startTime).count();
	dynamicMeshMetrics.totalUpdateTime += updateTime;

	// 更新位置记录
	for (int elemID = 0; elemID < localMesh->GetElementNumberReal(); elemID++)
	{
		previousPositions[elemID] = localMesh->GetElement(elemID).GetCenter();
	}

	return incrementalSuccess;
}

}
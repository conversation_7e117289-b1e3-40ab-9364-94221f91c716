﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OverDefines.h
//! <AUTHOR>
//! @brief 重叠网格数据类型定义
//! @date 2024-03-12
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_overDefines_
#define _specialModule_oversetMesh_overDefines_

// 标准库头文件
#include <cstddef>
#include <vector>
#include <cmath>
#include <set>
#include <map>
#include <memory>
#include <queue>
#include <algorithm>
#include <functional>

// feilian库头文件
#include "basic/common/Configuration.h"
#include "basic/common/ConfigUtility.h"
#include "sourceFlow/package/FlowPackage.h"
#include "meshProcess/zone/ZoneManager.h"
#include "basic/mesh/Mesh.h"
#include "meshProcess/wallDistance/KDT_utilities.h"

// MPI 相关头文件
#include "basic/common/BoostLib.h"
#include "basic/common/MPI.h"

namespace Overset
{
    // MPI 通信器类型别名（条件编译）
#if defined(_BaseParallelMPI_)
    using MPICommunicator = boost::mpi::communicator;
    using MPIRequest = boost::mpi::request;
#else
    // 非MPI环境下的占位符类型
    struct MPICommunicator
    {
    };
    struct MPIRequest
    {
    };
#endif

    /**
     * @brief 标准容器简化别名
     *
     */
    template <class T>
    using List = std::vector<T, std::allocator<T>>;
    template <class T1, class T2>
    using Pair = std::pair<T1, T2>;
    template <class T1, class T2>
    using Map = std::map<T1, T2>;
    template <class T>
    using Set = std::set<T>;
    template <class T>
    using UniquePtr = std::unique_ptrT > ;

    /**
     * @brief 重叠网格单元类型枚举
     *
     */
    enum ElemOverType
    {
        HOLE = -1,     // 洞单元或节点
        ACCEPTOR = 0,  // 插值单元或节点
        CALCULATED = 1 // 计算单元或节点
    };

    /**
     * @brief 重叠装配方法
     *
     */
    enum AssembleMethod
    {
        ElemBased = 1, // 直接计算网格单元壁面距离，然后判断单元重叠类型
        NodeBased = 2  // 先计算网格点壁面距离，然后判断点重叠类型，再判断单元重叠类型
    };
} // namespace Overset

#endif
﻿#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

namespace Overset
{
void OversetMesh::Initialize()
{
	// 重叠装配内部参数设置
	maxAdvanceNum = 3;
	minAdvanceNum = 0;
	interpolationLayerNum = 1;
	verboseInfo = true;
	assembleMethod = AssembleMethod::ElemBased;	

	// 获取网格基本信息
	dim = localMesh->GetMeshDimension();

	// 并行信息
	processorID = GetMPIRank();
	nProcessor = GetMPISize();

	// 获取重叠边界的当地编号
	localOversetPatchID = -1;
	for (int bcI = 0; bcI < localMesh->GetBoundarySize(); bcI++)
	{
		const int &globalID = localMesh->GetBoundaryIDGlobal(bcI);
		if (flowConfig.JudgeOversetGlobal(globalID))
		{
			localOversetPatchID = bcI;
			break;
		}
	}

	// 获取多域信息
	zoneManager = flowPackage.GetZoneManager();
	n_Zones = zoneManager->GetZoneNum();
	myZoneID = localMesh->GetMeshZoneID();

	// 全局插值映射关系初始化
	commitedAcceptors.clear();
	commitedAcceptors.resize(nProcessor);
	commitedDonors.clear();
	commitedDonors.resize(nProcessor);

	// 强制转换为SubMesh，重叠装配完毕后，更新粗网格单元类型
	subMesh = (SubMesh *)localMesh;

	// 将插值单元作为贡献单元的标记置0
	acceptorDonorFlag.resize(localMesh->GetElementNumberReal(), 0);
}
void OversetMesh::GetOversetPatchElemID()
{
	oversetPatchElemID.clear();
	for (int faceI = 0; faceI < localMesh->GetBoundaryFaceSize(localOversetPatchID); faceI++)
	{
		const int &faceID = localMesh->GetBoundaryFaceID(localOversetPatchID, faceI);
		// Set<int> neiElem;
		// this->GetNodeNeighbour(localMesh->GetFace(faceID), neiElem);
		// oversetPatchElemID.insert(neiElem.begin(), neiElem.end());
		const Face &face = localMesh->GetFace(faceID);
		oversetPatchElemID.insert(face.GetOwnerID());
	}
}

void OversetMesh::CheckInputs()
{
	if (processorID > 0)
		return;

	Print("重叠网格输入检查......");

	/*目前已改为有重叠边界存在时才创建OversetMesh对象，此项不再检查
	bool oversetBCflag = false;
	for (int i = 0; i < flowConfig.GetGlobalBoundaryNameList().size(); i++)
	{
	if (flowConfig.JudgeOversetGlobal(i))
	{
	oversetBCflag = true;
	}
	}
	if (!oversetBCflag)
	{
	FatalError(" OversetMesh: 边界条件设置中不存在\"OVERSET\"类型的边界，请重新前处理);
	}
	*/
	if (n_Zones < 2)
	{
		FatalError(" OversetMesh: 网格子域数量不符合要求!");
		return;
	}
	if (nProcessor < n_Zones)
	{
		FatalError(" OversetMesh: 并行进程数设置不符合要求!");
		return;
	}

	// // 检查flowConfig的插值方法，并设置OversetRegion中的插值方法
	// // 目前flowConfig中的插值类型与oversetRegion中的插值类型数据未打通，暂时在这里中转TODO
	// OversetRegion oversetRegion = localMesh->GetOversetRegion();
	// switch (interpolationType)
	// {
	// case OversetType::InverseDistance:
	// {
	// 	Print("重叠边界插值方法: InverseDistance");
	// 	oversetRegion.SetInterpolationType(oversetRegion.InverseDistance);
	// 	break;
	// }
	// case OversetType::Linear:
	// {
	// 	Print("重叠边界插值方法: Linear");
	// 	oversetRegion.SetInterpolationType(oversetRegion.Linear);
	// 	break;
	// }
	// case OversetType::LeastSquare:
	// {
	// 	Print("重叠边界插值方法: LeastSquare");
	// 	oversetRegion.SetInterpolationType(oversetRegion.LeastSquare);
	// 	break;
	// }
	// default:
	// {
	// 	FatalError("OversetMesh: 未知的插值方法......");
	// }
	// }

	Print("~~~~~~~~~~重叠网格输入满足基本要求~~~~~~~~~~");
}

void OversetMesh::CreateNodeNeiElem()
{
	nodeNeiElem.clear();
	nodeNeiElem.resize(localMesh->GetNodeNumber());

	const int &elemNum = localMesh->GetElementNumberAll();
	for (int elemID = 0; elemID < elemNum; elemID++)
	{
		const Element &elem = localMesh->GetElement(elemID);
		const int &nodeNum = elem.GetNodeSize();
		for (int i = 0; i < nodeNum; i++)
		{
			const int &nodeID = elem.GetNodeID(i);
			nodeNeiElem[nodeID].push_back(elemID);
		}
	}
}
}